import java.time.LocalDate;

class SpecificDateAnalysis {
    public static void main(String[] args) {
        System.out.println("=== ANALYZING SPECIFIC FAILURE DATES ===");
        
        // Test the exact dates you mentioned that fail
        LocalDate[] failureDates = {
            LocalDate.of(2025, 5, 1),   // May 1, 2025 - FAILS
            LocalDate.of(2025, 7, 30),  // July 30, 2025 - FAILS  
            LocalDate.of(2025, 7, 31),  // July 31, 2025 - FAILS
        };
        
        // Test dates that don't fail for comparison
        LocalDate[] passingDates = {
            LocalDate.of(2025, 4, 30),  // April 30, 2025 - PASSES
            LocalDate.of(2025, 5, 2),   // May 2, 2025 - PASSES (assumption)
            LocalDate.of(2025, 7, 29),  // July 29, 2025 - PASSES (assumption)
        };
        
        System.out.println("FAILURE DATES:");
        for (LocalDate date : failureDates) {
            analyzeSpecificDate(date);
        }
        
        System.out.println("\nPASSING DATES (for comparison):");
        for (LocalDate date : passingDates) {
            analyzeSpecificDate(date);
        }
        
        System.out.println("\n=== PATTERN ANALYSIS ===");
        findPattern();
    }
    
    static void analyzeSpecificDate(LocalDate currentDate) {
        System.out.println("\nDate: " + currentDate + " (" + currentDate.getDayOfWeek() + ")");
        
        // Calculate what the test does
        LocalDate cutoffDate = currentDate.minusMonths(6);
        LocalDate test5m29d = currentDate.minusMonths(5).minusDays(29);
        LocalDate test5m30d = currentDate.minusMonths(5).minusDays(30);
        
        System.out.println("  Current: " + currentDate);
        System.out.println("  6 months ago: " + cutoffDate);
        System.out.println("  5m29d ago: " + test5m29d + " -> " + getResult(test5m29d, cutoffDate));
        System.out.println("  5m30d ago: " + test5m30d + " -> " + getResult(test5m30d, cutoffDate));
        
        // Show the month arithmetic step by step
        LocalDate step1 = currentDate.minusMonths(5);
        System.out.println("  Step-by-step:");
        System.out.println("    " + currentDate + " - 5 months = " + step1);
        System.out.println("    " + step1 + " - 29 days = " + test5m29d);
        System.out.println("    " + step1 + " - 30 days = " + test5m30d);
        
        // Check if this would cause test failures
        boolean fails = test5m29d.isBefore(cutoffDate) || test5m30d.isBefore(cutoffDate);
        System.out.println("  WOULD FAIL: " + fails);
    }
    
    static String getResult(LocalDate testDate, LocalDate cutoffDate) {
        boolean isWithinLimit = !testDate.isBefore(cutoffDate);
        return isWithinLimit ? "PASS" : "FAIL (expected PASS)";
    }
    
    static void findPattern() {
        System.out.println("Looking for the pattern...");
        
        // Let's check what's special about these dates
        LocalDate may1 = LocalDate.of(2025, 5, 1);
        LocalDate july30 = LocalDate.of(2025, 7, 30);
        LocalDate july31 = LocalDate.of(2025, 7, 31);
        
        System.out.println("\nMay 1, 2025:");
        System.out.println("  - 5 months = " + may1.minusMonths(5) + " (Dec 1, 2024)");
        System.out.println("  - 6 months = " + may1.minusMonths(6) + " (Nov 1, 2024)");
        System.out.println("  Dec 1 - 30 days = " + may1.minusMonths(5).minusDays(30) + " (Nov 1, 2024)");
        System.out.println("  This equals the 6-month cutoff, so it should pass... hmm");
        
        System.out.println("\nJuly 30, 2025:");
        System.out.println("  - 5 months = " + july30.minusMonths(5) + " (Feb 28, 2025 - Feb has only 28 days!)");
        System.out.println("  - 6 months = " + july30.minusMonths(6) + " (Jan 30, 2025)");
        System.out.println("  Feb 28 - 30 days = " + july30.minusMonths(5).minusDays(30) + " (Jan 29, 2025)");
        System.out.println("  Jan 29 < Jan 30, so this FAILS!");
        
        System.out.println("\nJuly 31, 2025:");
        System.out.println("  - 5 months = " + july31.minusMonths(5) + " (Feb 28, 2025 - Feb has only 28 days!)");
        System.out.println("  - 6 months = " + july31.minusMonths(6) + " (Jan 31, 2025)");
        System.out.println("  Feb 28 - 30 days = " + july31.minusMonths(5).minusDays(30) + " (Jan 29, 2025)");
        System.out.println("  Jan 29 < Jan 31, so this FAILS!");
        
        System.out.println("\n*** PATTERN FOUND ***");
        System.out.println("The test fails when going back 5 months lands on a month with fewer days");
        System.out.println("than the current month, causing the day arithmetic to create dates that");
        System.out.println("are earlier than expected relative to the 6-month cutoff.");
        System.out.println("This particularly happens with February (28/29 days) when coming from");
        System.out.println("months with 30/31 days.");
    }
}
