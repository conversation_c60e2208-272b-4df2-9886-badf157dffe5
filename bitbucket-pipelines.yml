image: oscarpro/maven3-jdk18-bc
x-bash-env-steps:
  - &globals |
    export APP_GROUP_ID=ca.kai
    export APP_ARTIFACT_ID=kai-15-pilot
    export APP_PACKAGE_PATTERN="target/*.war"
    export APP_PACKAGING=war
    export APP_MYSQL_DB="oscar_test"
    export MAVEN_OPTS="-Xmx2048M -Xss1024k -XX:MetaspaceSize=512M -XX:MaxMetaspaceSize=1024M"
    echo APP_GROUP_ID:        $APP_GROUP_ID
    echo APP_ARTIFACT_ID:     $APP_ARTIFACT_ID
    echo APP_PACKAGE_PATTERN: $APP_PACKAGE_PATTERN
    echo APP_PACKAGING:       $APP_PACKAGING
    echo APP_MYSQL_DB:        $APP_MYSQL_DB
definitions:
  caches:
    frontend-node: frontend/node_modules
  services:
    mysql-57-est:
      image: oscarpro/mysql-57
      memory: 2048
      variables:
        MYSQL_DATABASE: 'oscar_test'
        MYSQL_USER: 'mysql'
        MYSQL_PASSWORD: 'mysql'
        MYSQL_ROOT_PASSWORD: 'mysql'
        TZ: 'America/Toronto'
    mysql-57-pst:
      image: oscarpro/mysql-57
      memory: 2048
      variables:
        MYSQL_DATABASE: 'oscar_test'
        MYSQL_USER: 'mysql'
        MYSQL_PASSWORD: 'mysql'
        MYSQL_ROOT_PASSWORD: 'mysql'
        TZ: 'America/Vancouver'
    docker:
      memory: 4096
  steps:
    build-backend: &build-backend
      name: Build backend
      caches:
        - maven
      script:
        - *globals
        - build-tools.sh mvn package
      artifacts:
        - target/**
    test: &test
      name: "Run unit tests"
      max-time: 20
      caches:
        - maven
      size: 2x
      script:
        - *globals
        - build-tools.sh mvn test
        # rename report file to prevent name conflicts with other steps
        - mkdir target/site/jacoco/
        - mv target/site/jacoco-unit-test-coverage-report/jacoco.xml target/site/jacoco/jacoco-unit-test.xml
      artifacts:
        - target/site/jacoco/*.xml
    integration-test-on: &integration-test-on
      name: "Run Integration tests (Ontario)"
      max-time: 20
      caches:
        - maven
      services:
        - mysql-57-est
      size: 2x
      script:
        - *globals
        # ON Tests
        - export TZ=America/Toronto && echo `date`
        - build-tools.sh mysql_load drugref pipeline/drugref.sql.gz
        - build-tools.sh migrator clone kai-15-database
        - build-tools.sh migrator run kai-15-database
        - build-tools.sh migrator clone oscar-pro-database
        - build-tools.sh migrator run oscar-pro-database
        - >
          mvn verify
          -e -s settings.xml
          -Dnexus.public=$NEXUS_PUBLIC
          -Doscar.dbinit.skip=true 
          -Dskip.unit.tests=true 
          -Dlicense.skip=true
          -Dmaven.javadoc.skip=true
          -Dmaven.antrun.skip=true
          -DexcludedGroups=BC
        # rename report file to prevent name conflicts with other steps
        - mv target/site/jacoco/jacoco.xml target/site/jacoco/jacoco-it-on.xml
      artifacts:
        - target/site/jacoco/*.xml
    integration-test-bc: &integration-test-bc
      name: "Run Integration tests (BC)"
      max-time: 20
      caches:
        - maven
      services:
        - mysql-57-pst
      size: 2x
      script:
        - *globals
        # BC Tests
        - export TZ=America/Vancouver && echo `date`
        - build-tools.sh mysql_load drugref pipeline/drugref.sql.gz
        - build-tools.sh migrator clone bc-15-database
        - build-tools.sh migrator run bc-15-database
        - build-tools.sh migrator clone oscar-pro-database
        - build-tools.sh migrator run oscar-pro-database
        - >
          mvn verify 
          -e -s settings.xml
          -Dnexus.public=$NEXUS_PUBLIC
          -Doscar.dbinit.skip=true 
          -Dskip.unit.tests=true 
          -Dlicense.skip=true
          -Dmaven.javadoc.skip=true
          -Dmaven.antrun.skip=true
          -DexcludedGroups=ON
        # rename report file to prevent name conflicts with other steps
        - mv target/site/jacoco/jacoco.xml target/site/jacoco/jacoco-it-bc.xml
      artifacts:
        - target/site/jacoco/*.xml
    deploy-release: &deploy-release
      name: "Deploy release to Nexus server"
      caches:
        - frontend-node
        - maven
      script:
        - *globals
        - export URL=$NEXUS_URL
        - build-tools.sh mvn deploy RELEASE_MAIN $NEXUS_URL
    # Provides legacy OSCAR Pro version scheme
    deploy-release-legacy: &deploy-release-legacy
      name: "Deploy release to Nexus server"
      caches:
        - frontend-node
        - maven
      script:
        - *globals
        - build-tools.sh mvn deploy LEGACY $NEXUS_URL
    deploy-snapshot: &deploy-snapshot
      name: "Deploy snapshot to Nexus server"
      caches:
        - frontend-node
        - maven
      script:
        - *globals
        - build-tools.sh mvn deploy SNAPSHOT $NEXUS_SNAPSHOT_URL
    finalize-release: &finalize-release
      name: "Merge release into main and create PR to develop"
      script:
        - bash release-tools.sh release
    create-release-from-develop: &create-release-from-develop
      name: "Create release from develop branch"
      script:
        - bash release-tools.sh branch
pipelines:
  # At the minimum build with maven
  default:
    - step: *build-backend
    - parallel:
        - step: *test
        - step: *integration-test-on
        - step: *integration-test-bc
  branches:
    release/*:
      - step: *build-backend
      - parallel:
          - step: *test
          - step: *integration-test-on
          - step: *integration-test-bc
      - step: *deploy-snapshot
    develop:
      - step: *build-backend
      - parallel:
          - step: *test
          - step: *integration-test-on
          - step: *integration-test-bc
      - step: *deploy-snapshot
  tags:
    release-*:
      - step: *build-backend
      - parallel:
          - step: *test
          - step: *integration-test-on
          - step: *integration-test-bc
      - step: *deploy-release
  custom:
    test:
      - step: *build-backend
      - parallel:
          - step: *test
          - step: *integration-test-on
          - step: *integration-test-bc
    qa-build:
      - step: *build-backend
      - parallel:
          - step: *test
          - step: *integration-test-on
          - step: *integration-test-bc
      - step: *deploy-snapshot
    legacy-release:
      - step: *build-backend
      - step: *test
      - step: *deploy-release-legacy
    finalize-release:
      - step:
          <<: *finalize-release
    create-release-branch:
      - variables:
          - name: RELEASE_VERSION
      - step:
          <<: *create-release-from-develop